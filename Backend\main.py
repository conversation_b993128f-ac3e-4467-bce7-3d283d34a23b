from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.routes import router as chat_router
from api.analytics import router as analytics_router
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv
import os
from urllib.parse import urlparse
from api.transcribe import app as transcribe_router
from api.tts_route import app as tts_router
from utils.logger import setup_logger
from services.api_usage_tracker import APIUsageTracker, APIUsageMiddleware
from services.analytics_websocket import analytics_ws_manager
load_dotenv()
url = os.getenv("URL")
parsed_url = urlparse(url)
host = parsed_url.hostname
port = parsed_url.port

app = FastAPI(title="AI Studio V2",
              description="Enhanced AI Studio with PDF Page Number Support and Live Analytics")

# Initialize API usage tracker
api_tracker = APIUsageTracker()

# Add API usage tracking middleware
app.add_middleware(APIUsageMiddleware, tracker=api_tracker)

# Initialize logger on startup


@app.on_event("startup")
async def startup_event():
    setup_logger("ai_studio", "INFO")
    print("🚀 AI Studio V2 Backend starting up...")
    print("📄 PDF Page Number Support: ENABLED")
    print("🔍 Enhanced logging: ENABLED")
    print("📊 Live Analytics Dashboard: ENABLED")
    print("🔌 WebSocket Analytics: ENABLED")

    # Initialize analytics table if needed
    try:
        from utils.snowflake_setup import create_api_usage_table
        create_api_usage_table()
        print("✅ Analytics database ready")
    except Exception as e:
        print(f"⚠️  Analytics database setup warning: {e}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://ai-studio-innosquares.duckdns.org",
        "http://ai-studio-innosquares.duckdns.org",
        "http://**************:5177",
        "http://localhost:5177",
        "*"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/static", StaticFiles(directory="plugins_repo"), name="plugins_repo")
app.include_router(chat_router)
app.include_router(transcribe_router)
app.include_router(tts_router)
app.include_router(analytics_router)
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host=host, port=port, reload=True)

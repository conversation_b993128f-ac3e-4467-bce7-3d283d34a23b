from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from api.routes import router as chat_router
from api.analytics import router as analytics_router
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv
import os
from urllib.parse import urlparse
from api.transcribe import app as transcribe_router
from api.tts_route import app as tts_router
from utils.logger import setup_logger
from services.api_usage_tracker import APIUsageTracker, APIUsageMiddleware
from services.analytics_websocket import analytics_ws_manager
load_dotenv()
url = os.getenv("URL")
parsed_url = urlparse(url)
host = parsed_url.hostname
port = parsed_url.port

app = FastAPI(title="AI Studio V2",
              description="Enhanced AI Studio with PDF Page Number Support and Live Analytics")

# Initialize API usage tracker
api_tracker = APIUsageTracker()

# Add API usage tracking middleware
app.add_middleware(APIUsageMiddleware, tracker=api_tracker)

# Initialize logger on startup


@app.on_event("startup")
async def startup_event():
    setup_logger("ai_studio", "INFO")
    print("🚀 AI Studio V2 Backend starting up...")
    print("📄 PDF Page Number Support: ENABLED")
    print("🔍 Enhanced logging: ENABLED")
    print("📊 Live Analytics Dashboard: ENABLED")
    print("🔌 WebSocket Analytics: ENABLED")

    # Initialize analytics table if needed
    try:
        from utils.snowflake_setup import create_api_usage_table
        create_api_usage_table()
        print("✅ Analytics database ready")
    except Exception as e:
        print(f"⚠️  Analytics database setup warning: {e}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://ai-studio-innosquares.duckdns.org",
        "http://ai-studio-innosquares.duckdns.org",
        "http://**************:5177",
        "http://localhost:5177",
        "*"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount("/static", StaticFiles(directory="plugins_repo"), name="plugins_repo")
app.include_router(chat_router, prefix="/api")
app.include_router(transcribe_router, prefix="/api")
app.include_router(tts_router, prefix="/api")
app.include_router(analytics_router, prefix="/api")

# Test endpoint to verify WebSocket path is reachable
@app.get("/ws/test")
async def test_ws_path():
    return {"message": "WebSocket path is reachable", "status": "ok"}

# Simple WebSocket test endpoint
@app.websocket("/ws/test-simple")
async def websocket_test(websocket: WebSocket):
    print("🔌 Simple WebSocket test endpoint reached")
    await websocket.accept()
    await websocket.send_text("Hello WebSocket!")
    await websocket.close()

# WebSocket endpoint (not under /api prefix)
@app.websocket("/ws/analytics")
async def websocket_analytics(websocket: WebSocket):
    """WebSocket endpoint for real-time analytics streaming"""

    print("🔌 WebSocket endpoint reached - about to accept connection")

    try:
        # Accept the connection first
        await websocket.accept()
        print("✅ WebSocket connection accepted")

        print(f"🔌 WebSocket connection from origin: {websocket.headers.get('origin')}")
        print(f"🔌 WebSocket headers: {dict(websocket.headers)}")

        # Now add to analytics manager
        analytics_ws_manager.active_connections.add(websocket)
        print("✅ Analytics WebSocket client connected successfully")

        # Send initial data
        await analytics_ws_manager.send_initial_data(websocket)

    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        raise

    try:
        # Send initial data
        await analytics_ws_manager.send_initial_data(websocket)

        # Keep connection alive - the broadcasting is handled by the manager
        import asyncio
        while True:
            try:
                # Wait for messages from client with timeout
                message = await asyncio.wait_for(websocket.receive_json(), timeout=1.0)
                await analytics_ws_manager.handle_client_message(websocket, message)
            except asyncio.TimeoutError:
                # No message received, continue to keep connection alive
                continue
            except WebSocketDisconnect:
                print("Analytics WebSocket client disconnected")
                break
            except Exception as e:
                print(f"WebSocket receive error: {e}")
                await asyncio.sleep(1)
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        analytics_ws_manager.disconnect(websocket)
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host=host, port=port, reload=True)

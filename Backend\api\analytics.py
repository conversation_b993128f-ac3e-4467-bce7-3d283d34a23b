from fastapi import APIRouter, HTTPException, Query, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from typing import Optional
import asyncio
import json
from services.analytics_service import AnalyticsService
from services.analytics_websocket import analytics_ws_manager
from utils.logger import get_logger

logger = get_logger("analytics_api")

router = APIRouter()
analytics_service = AnalyticsService()


@router.get("/analytics/metrics")
async def get_dashboard_metrics():
    """Get overall dashboard metrics"""
    try:
        metrics = analytics_service.get_dashboard_metrics()
        return JSONResponse(content=metrics)
    except Exception as e:
        logger.error(f"Error getting dashboard metrics: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve dashboard metrics")


@router.get("/analytics/usage")
async def get_usage_data(
    time_range: Optional[str] = Query(
        "30D", description="Time range: 7D, 30D, 90D, 1Y"),
    custom_days: Optional[int] = Query(
        None, description="Custom number of days")
):
    """Get usage time series data"""
    try:
        if custom_days:
            # Return custom usage data
            series_data = analytics_service.get_custom_usage_data(custom_days)
            return JSONResponse(content={"series": series_data})
        else:
            # Return standard time range data
            usage_series = analytics_service.get_usage_series()
            if time_range in usage_series:
                return JSONResponse(content={"series": usage_series[time_range]})
            else:
                return JSONResponse(content={"series": usage_series.get("30D", [])})
    except Exception as e:
        logger.error(f"Error getting usage data: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve usage data")


@router.get("/analytics/models")
async def get_model_distribution():
    """Get model usage distribution"""
    try:
        distribution = analytics_service.get_model_distribution()
        return JSONResponse(content={"distribution": distribution})
    except Exception as e:
        logger.error(f"Error getting model distribution: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve model distribution")


@router.get("/analytics/endpoints")
async def get_endpoint_stats():
    """Get statistics by endpoint"""
    try:
        stats = analytics_service.get_endpoint_stats()
        return JSONResponse(content={"endpoints": stats})
    except Exception as e:
        logger.error(f"Error getting endpoint stats: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve endpoint statistics")


@router.get("/analytics/realtime")
async def get_realtime_metrics():
    """Get real-time metrics snapshot"""
    try:
        metrics = analytics_service.get_real_time_metrics()
        return JSONResponse(content=metrics)
    except Exception as e:
        logger.error(f"Error getting real-time metrics: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to retrieve real-time metrics")


# WebSocket endpoint moved to main.py to avoid /api prefix

# Connection status endpoint


@router.get("/analytics/connections")
async def get_analytics_connections():
    """Get analytics WebSocket connection status"""
    return JSONResponse(content={
        "active_connections": analytics_ws_manager.get_connection_count(),
        "is_broadcasting": analytics_ws_manager.is_broadcasting
    })

# Test endpoint for debugging


@router.get("/analytics/test")
async def test_analytics():
    """Test endpoint to verify analytics data"""
    try:
        # Get a quick sample of data
        metrics = analytics_service.get_dashboard_metrics()
        return JSONResponse(content={
            "status": "working",
            "sample_metrics": metrics,
            "message": "Analytics service is operational"
        })
    except Exception as e:
        logger.error(f"Analytics test failed: {e}")
        return JSONResponse(status_code=500, content={
            "status": "error",
            "error": str(e),
            "message": "Analytics service has issues"
        })

# Health check endpoint


@router.get("/analytics/health")
async def analytics_health():
    """Health check for analytics service"""
    try:
        # Test database connection
        analytics_service.get_connection().close()
        return JSONResponse(content={
            "status": "healthy",
            "service": "analytics",
            "timestamp": analytics_service.get_real_time_metrics().get("timestamp")
        })
    except Exception as e:
        logger.error(f"Analytics health check failed: {e}")
        raise HTTPException(
            status_code=503, detail="Analytics service unavailable")
